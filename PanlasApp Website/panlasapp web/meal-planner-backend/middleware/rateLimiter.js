// Simple in-memory rate limiter (for production, use Redis)
class RateLimiter {
  constructor() {
    this.attempts = new Map(); // Store login attempts by IP
    this.blockedIPs = new Map(); // Store blocked IPs
  }

  // Clean up old entries every hour
  cleanup() {
    const now = Date.now();
    const oneHour = 60 * 60 * 1000;

    // Clean attempts older than 1 hour
    for (const [ip, data] of this.attempts.entries()) {
      if (now - data.firstAttempt > oneHour) {
        this.attempts.delete(ip);
      }
    }

    // Clean blocked IPs older than 1 hour
    for (const [ip, blockedUntil] of this.blockedIPs.entries()) {
      if (now > blockedUntil) {
        this.blockedIPs.delete(ip);
      }
    }
  }

  // Check if IP is currently blocked
  isBlocked(ip) {
    const blockedUntil = this.blockedIPs.get(ip);
    if (blockedUntil && Date.now() < blockedUntil) {
      return true;
    }
    if (blockedUntil) {
      this.blockedIPs.delete(ip); // Remove expired block
    }
    return false;
  }

  // Record a failed login attempt
  recordFailedAttempt(ip) {
    const now = Date.now();
    const maxAttempts = 5;
    const windowMs = 15 * 60 * 1000; // 15 minutes
    const blockDuration = 60 * 60 * 1000; // 1 hour

    if (!this.attempts.has(ip)) {
      this.attempts.set(ip, {
        count: 1,
        firstAttempt: now,
        lastAttempt: now
      });
      return false; // Not blocked yet
    }

    const attempts = this.attempts.get(ip);
    
    // Reset if window has passed
    if (now - attempts.firstAttempt > windowMs) {
      this.attempts.set(ip, {
        count: 1,
        firstAttempt: now,
        lastAttempt: now
      });
      return false;
    }

    // Increment attempt count
    attempts.count++;
    attempts.lastAttempt = now;

    // Block if too many attempts
    if (attempts.count >= maxAttempts) {
      this.blockedIPs.set(ip, now + blockDuration);
      this.attempts.delete(ip); // Clear attempts after blocking
      return true;
    }

    return false;
  }

  // Record a successful login (clear attempts)
  recordSuccessfulLogin(ip) {
    this.attempts.delete(ip);
  }

  // Get remaining attempts before block
  getRemainingAttempts(ip) {
    const maxAttempts = 5;
    const attempts = this.attempts.get(ip);
    if (!attempts) return maxAttempts;
    return Math.max(0, maxAttempts - attempts.count);
  }

  // Get time until unblock (in minutes)
  getTimeUntilUnblock(ip) {
    const blockedUntil = this.blockedIPs.get(ip);
    if (!blockedUntil) return 0;
    const remaining = blockedUntil - Date.now();
    return Math.ceil(remaining / (60 * 1000)); // Convert to minutes
  }
}

const rateLimiter = new RateLimiter();

// Clean up every hour
setInterval(() => {
  rateLimiter.cleanup();
}, 60 * 60 * 1000);

// Middleware for login rate limiting
const loginRateLimit = (req, res, next) => {
  const ip = req.ip || req.connection.remoteAddress;
  
  if (rateLimiter.isBlocked(ip)) {
    const timeUntilUnblock = rateLimiter.getTimeUntilUnblock(ip);
    return res.status(429).json({
      message: `Too many login attempts. Please try again in ${timeUntilUnblock} minutes.`,
      retryAfter: timeUntilUnblock
    });
  }

  // Add rate limiter to request for use in login controller
  req.rateLimiter = rateLimiter;
  req.clientIP = ip;
  next();
};

// Middleware for general API rate limiting
const apiRateLimit = (req, res, next) => {
  // Simple rate limiting for API calls
  const ip = req.ip || req.connection.remoteAddress;
  const now = Date.now();
  const windowMs = 60 * 1000; // 1 minute
  const maxRequests = 100; // 100 requests per minute

  if (!rateLimiter.apiRequests) {
    rateLimiter.apiRequests = new Map();
  }

  const requests = rateLimiter.apiRequests.get(ip) || [];
  const recentRequests = requests.filter(time => now - time < windowMs);

  if (recentRequests.length >= maxRequests) {
    return res.status(429).json({
      message: 'Too many requests. Please try again later.',
      retryAfter: Math.ceil(windowMs / 1000)
    });
  }

  recentRequests.push(now);
  rateLimiter.apiRequests.set(ip, recentRequests);

  next();
};

module.exports = {
  loginRateLimit,
  apiRateLimit,
  rateLimiter
};
