const mongoose = require('mongoose');
const readline = require('readline');
const User = require('../models/User');
require('dotenv').config();

// Users to delete
const USERS_TO_DELETE = [
  '<EMAIL>',
  '<EMAIL>'
];

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer);
    });
  });
}

async function deleteUsersInteractive() {
  try {
    console.log('🗑️  Interactive User Deletion Script');
    console.log('=' .repeat(50));
    console.log('📧 Target emails:', USERS_TO_DELETE);
    
    // Connect to MongoDB
    console.log('\n📡 Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('✅ Connected to MongoDB successfully');

    // Get database info
    const dbName = mongoose.connection.name;
    console.log('📊 Database name:', dbName);

    // Check if users exist before deletion
    console.log('\n🔍 Checking for existing users...');
    const existingUsers = [];
    
    for (const email of USERS_TO_DELETE) {
      const user = await User.findOne({ email: email.toLowerCase() });
      if (user) {
        existingUsers.push(user);
        console.log(`\n✅ Found user: ${email}`);
        console.log(`   - ID: ${user._id}`);
        console.log(`   - Username: ${user.username}`);
        console.log(`   - Name: ${user.firstName} ${user.lastName}`);
        console.log(`   - Created: ${user.createdAt}`);
        console.log(`   - Email Verified: ${user.isEmailVerified}`);
        console.log(`   - Last Login: ${user.lastLogin || 'Never'}`);
      } else {
        console.log(`❌ User not found: ${email}`);
      }
    }

    if (existingUsers.length === 0) {
      console.log('\n🎉 No users found to delete. Script completed.');
      return;
    }

    // Show summary and ask for confirmation
    console.log('\n📊 DELETION SUMMARY:');
    console.log('='.repeat(50));
    console.log(`👥 Users found: ${existingUsers.length}`);
    console.log(`❌ Users not found: ${USERS_TO_DELETE.length - existingUsers.length}`);
    
    console.log('\n⚠️  WARNING: This will permanently delete the following users:');
    existingUsers.forEach((user, index) => {
      console.log(`${index + 1}. ${user.email} (${user.firstName} ${user.lastName})`);
    });
    
    console.log('\n📝 This action cannot be undone!');
    
    // Ask for confirmation
    const confirmation = await askQuestion('\n❓ Do you want to proceed with deletion? (yes/no): ');
    
    if (confirmation.toLowerCase() !== 'yes' && confirmation.toLowerCase() !== 'y') {
      console.log('\n❌ Deletion cancelled by user.');
      return;
    }

    // Final confirmation
    const finalConfirmation = await askQuestion('\n❓ Are you absolutely sure? Type "DELETE" to confirm: ');
    
    if (finalConfirmation !== 'DELETE') {
      console.log('\n❌ Deletion cancelled. You must type "DELETE" exactly to confirm.');
      return;
    }

    // Delete users
    console.log('\n🗑️  Starting deletion process...');
    let deletedCount = 0;

    for (const email of USERS_TO_DELETE) {
      try {
        console.log(`\n🔄 Processing: ${email}`);
        
        const result = await User.deleteOne({ email: email.toLowerCase() });
        
        if (result.deletedCount > 0) {
          console.log(`✅ Successfully deleted: ${email}`);
          deletedCount++;
        } else {
          console.log(`❌ User not found: ${email}`);
        }
      } catch (error) {
        console.error(`❌ Error deleting ${email}:`, error.message);
      }
    }

    // Summary
    console.log('\n📊 FINAL SUMMARY:');
    console.log('='.repeat(50));
    console.log(`✅ Users deleted: ${deletedCount}`);
    console.log(`📧 Total processed: ${USERS_TO_DELETE.length}`);

    // Verify deletion
    console.log('\n🔍 Verifying deletion...');
    for (const email of USERS_TO_DELETE) {
      const user = await User.findOne({ email: email.toLowerCase() });
      if (user) {
        console.log(`⚠️  WARNING: User still exists: ${email}`);
      } else {
        console.log(`✅ Confirmed deleted: ${email}`);
      }
    }

    // Get remaining user count
    const totalUsers = await User.countDocuments();
    console.log(`\n📈 Total users remaining in database: ${totalUsers}`);

    console.log('\n🎉 User deletion script completed successfully!');

  } catch (error) {
    console.error('❌ Error in deletion script:', error);
    console.error('Stack trace:', error.stack);
  } finally {
    // Close readline interface
    rl.close();
    
    // Close database connection
    if (mongoose.connection.readyState === 1) {
      await mongoose.connection.close();
      console.log('\n📡 Database connection closed');
    }
  }
}

// Handle script interruption
process.on('SIGINT', async () => {
  console.log('\n\n⚠️  Script interrupted by user');
  rl.close();
  if (mongoose.connection.readyState === 1) {
    await mongoose.connection.close();
    console.log('📡 Database connection closed');
  }
  process.exit(0);
});

// Run the script
if (require.main === module) {
  deleteUsersInteractive().then(() => {
    process.exit(0);
  }).catch((error) => {
    console.error('Script failed:', error);
    process.exit(1);
  });
}

module.exports = deleteUsersInteractive;
