const mongoose = require('mongoose');
require('dotenv').config({ path: '../.env' });

// Import the User model
const User = require('../models/User');

// Function to generate random date of birth for family members
function generateRandomDOB() {
  const currentYear = new Date().getFullYear();
  const minYear = currentYear - 80; // Max age 80
  const maxYear = currentYear - 5;  // Min age 5
  
  const year = Math.floor(Math.random() * (maxYear - minYear + 1)) + minYear;
  const month = Math.floor(Math.random() * 12); // 0-11
  const day = Math.floor(Math.random() * 28) + 1; // 1-28 (safe for all months)
  
  return new Date(year, month, day);
}

async function migrateFavoritesAndDOB() {
  try {
    console.log('🔗 Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    console.log('📊 Starting migration...');
    
    // Get all users
    const users = await User.find({});
    console.log(`Found ${users.length} users to process`);

    let migratedPlansCount = 0;
    let addedDOBCount = 0;
    let usersWithFamilyMembers = 0;

    for (const user of users) {
      let userUpdated = false;

      // 1. Migrate savedMealPlans to favoriteMealPlans
      if (user.savedMealPlans && user.savedMealPlans.length > 0) {
        console.log(`📋 Migrating ${user.savedMealPlans.length} saved meal plans for user: ${user.email}`);
        
        // Initialize favoriteMealPlans if it doesn't exist
        if (!user.favoriteMealPlans) {
          user.favoriteMealPlans = [];
        }

        // Migrate each saved meal plan to favorite meal plan
        for (const savedPlan of user.savedMealPlans) {
          // Check if this plan is already in favorites (avoid duplicates)
          const existsInFavorites = user.favoriteMealPlans.some(
            fav => fav.plan && savedPlan.plan && fav.plan.toString() === savedPlan.plan.toString()
          );

          if (!existsInFavorites) {
            user.favoriteMealPlans.push({
              name: savedPlan.name || 'Favorite Meal Plan',
              plan: savedPlan.plan,
              date: new Date().toISOString().split('T')[0], // Today's date as default
              totalCalories: 0, // Will be calculated later
              totalMeals: 0, // Will be calculated later
              mealTypes: ['breakfast', 'lunch', 'dinner'], // Default meal types
              createdAt: savedPlan.createdAt || new Date(),
              addedAt: new Date()
            });
            migratedPlansCount++;
          }
        }

        // Clear savedMealPlans after migration
        user.savedMealPlans = [];
        userUpdated = true;
      }

      // 2. Add DOB to family members who don't have it
      if (user.familyMembers && user.familyMembers.length > 0) {
        usersWithFamilyMembers++;
        console.log(`👨‍👩‍👧‍👦 Processing ${user.familyMembers.length} family members for user: ${user.email}`);
        
        for (const member of user.familyMembers) {
          if (!member.dateOfBirth) {
            member.dateOfBirth = generateRandomDOB();
            addedDOBCount++;
            userUpdated = true;
            console.log(`   ✅ Added DOB for ${member.name}: ${member.dateOfBirth.toDateString()}`);
          } else {
            console.log(`   ⏭️  ${member.name} already has DOB: ${member.dateOfBirth.toDateString()}`);
          }
        }
      }

      // Save user if updated
      if (userUpdated) {
        await user.save();
        console.log(`💾 Updated user: ${user.email}`);
      }
    }

    console.log('\n📈 Migration Summary:');
    console.log(`   ✅ Total users processed: ${users.length}`);
    console.log(`   📋 Meal plans migrated: ${migratedPlansCount}`);
    console.log(`   👨‍👩‍👧‍👦 Users with family members: ${usersWithFamilyMembers}`);
    console.log(`   🎂 DOBs added to family members: ${addedDOBCount}`);

    // Verify the migration
    console.log('\n🔍 Verifying migration...');
    
    const usersWithFavorites = await User.countDocuments({ 
      'favoriteMealPlans.0': { $exists: true } 
    });
    
    const usersWithSavedPlans = await User.countDocuments({ 
      'savedMealPlans.0': { $exists: true } 
    });

    const familyMembersWithDOB = await User.aggregate([
      { $unwind: '$familyMembers' },
      { $match: { 'familyMembers.dateOfBirth': { $exists: true, $ne: null } } },
      { $count: 'count' }
    ]);

    console.log(`   📊 Users with favorite meal plans: ${usersWithFavorites}`);
    console.log(`   📊 Users with saved meal plans (should be 0): ${usersWithSavedPlans}`);
    console.log(`   📊 Family members with DOB: ${familyMembersWithDOB[0]?.count || 0}`);

    if (usersWithSavedPlans === 0) {
      console.log('🎉 Migration successful! All saved meal plans converted to favorites.');
    } else {
      console.log('⚠️  Warning: Some users still have saved meal plans.');
    }

  } catch (error) {
    console.error('❌ Error during migration:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run the migration
migrateFavoritesAndDOB();
