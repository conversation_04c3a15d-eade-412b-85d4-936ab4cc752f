const nodemailer = require('nodemailer');
const crypto = require('crypto');

class EmailService {
  constructor() {
    this.initializeBrevoSMTP();
  }

  initializeBrevoSMTP() {
    try {
      // Validate required Brevo credentials
      if (!process.env.BREVO_SMTP_USER || !process.env.BREVO_SMTP_PASS) {
        throw new Error('Missing Brevo SMTP credentials. Please set BREVO_SMTP_USER and BREVO_SMTP_PASS in .env file');
      }

      // Initialize Brevo SMTP transporter
      this.transporter = nodemailer.createTransport({
        host: 'smtp-relay.brevo.com',
        port: 587,
        secure: false, // Use TLS
        auth: {
          user: process.env.BREVO_SMTP_USER,
          pass: process.env.BREVO_SMTP_PASS
        },
        // Additional options for better deliverability
        tls: {
          rejectUnauthorized: false
        }
      });

      console.log('📧 Email service initialized with Brevo SMTP');
      console.log('📧 SMTP User:', process.env.BREVO_SMTP_USER);
      console.log('📧 From Email:', process.env.BREVO_FROM_EMAIL || '<EMAIL>');

    } catch (error) {
      console.error('❌ Failed to initialize Brevo SMTP:', error.message);
      throw error;
    }
  }

  // Generate verification token
  generateVerificationToken() {
    return crypto.randomBytes(32).toString('hex');
  }

  // Generate OTP
  generateOTP() {
    return Math.floor(100000 + Math.random() * 900000).toString(); // 6-digit OTP
  }

  // Generate password reset token
  generatePasswordResetToken() {
    return crypto.randomBytes(32).toString('hex');
  }

  // Send email verification with OTP
  async sendVerificationEmail(email, username, verificationToken, otp) {
    const verificationUrl = `${process.env.FRONTEND_URL || 'http://localhost:3000'}/verify-email?token=${verificationToken}`;

    const mailOptions = {
      from: {
        name: process.env.BREVO_FROM_NAME || 'PanlasApp',
        address: process.env.BREVO_FROM_EMAIL || '<EMAIL>'
      },
      to: email,
      subject: 'Verify Your PanlasApp Account',
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Verify Your PanlasApp Account</title>
        </head>
        <body style="margin: 0; padding: 0; background-color: #f4f4f4;">
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background-color: #ffffff; padding: 20px;">
            <div style="text-align: center; margin-bottom: 30px; padding: 20px; background-color: #20C5AF; border-radius: 8px;">
              <h1 style="color: #ffffff; margin: 0; font-size: 28px;">PanlasApp</h1>
              <p style="color: #ffffff; margin: 5px 0 0 0; font-size: 14px;">Filipino Meal Planning Made Easy</p>
            </div>

            <h2 style="color: #333; margin-bottom: 20px;">Welcome to PanlasApp!</h2>
            <p style="color: #555; font-size: 16px; line-height: 1.5;">Hi ${username},</p>
            <p style="color: #555; font-size: 16px; line-height: 1.5;">
              Thank you for registering with PanlasApp! To complete your registration and start planning your Filipino meals,
              please verify your email address using the code below.
            </p>

            <div style="background-color: #f8f9fa; padding: 30px; border-radius: 8px; margin: 30px 0; text-align: center; border: 2px solid #20C5AF;">
              <h3 style="color: #333; margin-top: 0; font-size: 18px;">Your Verification Code:</h3>
              <div style="font-size: 36px; font-weight: bold; color: #20C5AF; letter-spacing: 8px; margin: 20px 0; font-family: 'Courier New', monospace;">
                ${otp}
              </div>
              <p style="color: #666; font-size: 14px; margin-bottom: 0;">
                Enter this 6-digit code in the PanlasApp to verify your account
              </p>
            </div>

            <div style="background-color: #fff3cd; padding: 15px; border-radius: 5px; border-left: 4px solid #ffc107; margin: 20px 0;">
              <p style="color: #856404; font-size: 14px; margin: 0;">
                <strong>⏰ Important:</strong> This verification code will expire in 10 minutes for security reasons.
              </p>
            </div>

            <p style="color: #555; font-size: 16px; line-height: 1.5;">
              Once verified, you'll be able to:
            </p>
            <ul style="color: #555; font-size: 16px; line-height: 1.5;">
              <li>Create personalized Filipino meal plans</li>
              <li>Track your dietary preferences and allergies</li>
              <li>Save your favorite recipes</li>
              <li>Plan meals for your whole family</li>
            </ul>

            <div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #eee; text-align: center;">
              <p style="color: #666; font-size: 12px; margin: 0;">
                If you didn't create an account with PanlasApp, please ignore this email.
              </p>
              <p style="color: #666; font-size: 12px; margin: 10px 0 0 0;">
                © 2025 PanlasApp. All rights reserved.
              </p>
            </div>
          </div>
        </body>
        </html>
      `,
      text: `
        Welcome to PanlasApp!

        Hi ${username},

        Thank you for registering with PanlasApp. To complete your registration, please verify your email address.

        Your verification code: ${otp}

        Enter this code in the app to verify your account.

        This verification code will expire in 10 minutes for security reasons.

        If you didn't create an account with PanlasApp, please ignore this email.

        © 2025 PanlasApp. All rights reserved.
      `
    };

    try {
      // Send email via Brevo SMTP
      console.log('\n📧 ===== SENDING EMAIL VIA BREVO =====');
      console.log('📧 From:', mailOptions.from);
      console.log('📧 To:', email);
      console.log('📧 Subject:', mailOptions.subject);
      console.log('📧 OTP Code:', otp);

      const info = await this.transporter.sendMail(mailOptions);

      console.log('✅ Email sent successfully via Brevo!');
      console.log('📧 Message ID:', info.messageId);
      console.log('📧 Response:', info.response);
      console.log('📧 Check Brevo dashboard for delivery status');
      console.log('=====================================\n');

      return true;
    } catch (error) {
      console.error('❌ Error sending verification email via Brevo:', error);
      console.error('📧 Error details:', error.message);

      // Log the OTP for debugging purposes
      console.log('\n📧 ===== EMAIL FAILED - OTP FOR DEBUGGING =====');
      console.log('📧 To:', email);
      console.log('📧 OTP Code:', otp);
      console.log('📧 Error:', error.message);
      console.log('===============================================\n');

      // Return false to indicate failure
      return false;
    }
  }

  async sendPasswordResetEmail(email, username, resetToken) {
    const resetUrl = `${process.env.FRONTEND_URL || 'http://localhost:3000'}/reset-password?token=${resetToken}`;

    const mailOptions = {
      from: {
        name: process.env.BREVO_FROM_NAME || 'PanlasApp',
        address: process.env.BREVO_FROM_EMAIL || '<EMAIL>'
      },
      to: email,
      subject: 'Reset Your PanlasApp Password',
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Reset Your PanlasApp Password</title>
        </head>
        <body style="margin: 0; padding: 0; background-color: #f4f4f4;">
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background-color: #ffffff; padding: 20px;">
            <div style="text-align: center; margin-bottom: 30px; padding: 20px; background-color: #20C5AF; border-radius: 8px;">
              <h1 style="color: #ffffff; margin: 0; font-size: 28px;">PanlasApp</h1>
              <p style="color: #ffffff; margin: 5px 0 0 0; font-size: 14px;">Filipino Meal Planning Made Easy</p>
            </div>

            <h2 style="color: #333; margin-bottom: 20px;">Password Reset Request</h2>
            <p style="color: #555; font-size: 16px; line-height: 1.5;">Hi ${username},</p>
            <p style="color: #555; font-size: 16px; line-height: 1.5;">
              We received a request to reset your password for your PanlasApp account.
              If you didn't make this request, you can safely ignore this email.
            </p>

            <div style="background-color: #f8f9fa; padding: 30px; border-radius: 8px; margin: 30px 0; text-align: center; border: 2px solid #20C5AF;">
              <h3 style="color: #333; margin-top: 0; font-size: 18px;">Reset Token:</h3>
              <div style="font-size: 24px; font-weight: bold; color: #20C5AF; letter-spacing: 3px; margin: 20px 0; font-family: 'Courier New', monospace; word-break: break-all;">
                ${resetToken}
              </div>
              <p style="color: #666; font-size: 14px; margin-bottom: 0;">
                Enter this token in the PanlasApp to reset your password
              </p>
            </div>

            <div style="background-color: #fff3cd; padding: 15px; border-radius: 5px; border-left: 4px solid #ffc107; margin: 20px 0;">
              <p style="color: #856404; font-size: 14px; margin: 0;">
                <strong>⏰ Important:</strong> This reset token will expire in 1 hour for security reasons.
              </p>
            </div>

            <div style="background-color: #f8d7da; padding: 15px; border-radius: 5px; border-left: 4px solid #dc3545; margin: 20px 0;">
              <p style="color: #721c24; font-size: 14px; margin: 0;">
                <strong>🔒 Security Note:</strong> If you didn't request this password reset, please contact our support team immediately.
              </p>
            </div>

            <div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #eee; text-align: center;">
              <p style="color: #666; font-size: 12px; margin: 0;">
                If you didn't request a password reset, please ignore this email or contact support if you have concerns.
              </p>
              <p style="color: #666; font-size: 12px; margin: 10px 0 0 0;">
                © 2025 PanlasApp. All rights reserved.
              </p>
            </div>
          </div>
        </body>
        </html>
      `,
      text: `
        Password Reset Request - PanlasApp

        Hi ${username},

        We received a request to reset your password for your PanlasApp account.

        Reset Token: ${resetToken}

        Enter this token in the PanlasApp to reset your password.

        This reset token will expire in 1 hour for security reasons.

        If you didn't request this password reset, please ignore this email.

        © 2025 PanlasApp. All rights reserved.
      `
    };

    try {
      // Send email via Brevo SMTP
      console.log('\n📧 ===== SENDING PASSWORD RESET EMAIL VIA BREVO =====');
      console.log('📧 From:', mailOptions.from);
      console.log('📧 To:', email);
      console.log('📧 Subject:', mailOptions.subject);
      console.log('📧 Reset Token:', resetToken);

      const info = await this.transporter.sendMail(mailOptions);

      console.log('✅ Password reset email sent successfully via Brevo!');
      console.log('📧 Message ID:', info.messageId);
      console.log('📧 Response:', info.response);
      console.log('📧 Check Brevo dashboard for delivery status');
      console.log('=======================================================\n');

      return true;
    } catch (error) {
      console.error('❌ Error sending password reset email via Brevo:', error);
      console.error('📧 Error details:', error.message);

      // Log the token for debugging purposes
      console.log('\n📧 ===== PASSWORD RESET EMAIL FAILED - TOKEN FOR DEBUGGING =====');
      console.log('📧 To:', email);
      console.log('📧 Reset Token:', resetToken);
      console.log('📧 Error:', error.message);
      console.log('===============================================================\n');

      // Return false to indicate failure
      return false;
    }
  }

  async sendWelcomeEmail(email, username) {
    console.log(`📧 Welcome email would be sent to: ${email}`);
    return true;
  }
}

module.exports = new EmailService();
