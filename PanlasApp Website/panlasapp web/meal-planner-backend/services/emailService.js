const nodemailer = require('nodemailer');
const crypto = require('crypto');

class EmailService {
  constructor() {
    // Check if email credentials are configured
    if (process.env.EMAIL_USER && process.env.EMAIL_PASS &&
        process.env.EMAIL_USER !== '<EMAIL>') {
      // Use real email service
      this.transporter = nodemailer.createTransport({
        service: 'gmail',
        auth: {
          user: process.env.EMAIL_USER,
          pass: process.env.EMAIL_PASS
        }
      });
      this.isTestMode = false;
    } else {
      // Use test mode (console logging)
      console.log('📧 Email service running in TEST MODE - emails will be logged to console');
      this.transporter = nodemailer.createTransport({
        streamTransport: true,
        newline: 'unix',
        buffer: true
      });
      this.isTestMode = true;
    }
  }

  // Generate verification token
  generateVerificationToken() {
    return crypto.randomBytes(32).toString('hex');
  }

  // Generate password reset token
  generatePasswordResetToken() {
    return crypto.randomBytes(32).toString('hex');
  }

  // Send email verification
  async sendVerificationEmail(email, username, verificationToken) {
    const verificationUrl = `${process.env.FRONTEND_URL}/verify-email?token=${verificationToken}`;
    
    const mailOptions = {
      from: process.env.EMAIL_USER,
      to: email,
      subject: 'Verify Your PanlasApp Account',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #20C5AF;">Welcome to PanlasApp!</h2>
          <p>Hi ${username},</p>
          <p>Thank you for registering with PanlasApp. Please verify your email address to complete your registration.</p>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${verificationUrl}" 
               style="background-color: #20C5AF; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
              Verify Email Address
            </a>
          </div>
          <p>Or copy and paste this link in your browser:</p>
          <p style="word-break: break-all; color: #666;">${verificationUrl}</p>
          <p style="color: #666; font-size: 12px;">This link will expire in 24 hours.</p>
          <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
          <p style="color: #666; font-size: 12px;">
            If you didn't create an account with PanlasApp, please ignore this email.
          </p>
        </div>
      `
    };

    try {
      if (this.isTestMode) {
        console.log('\n📧 ===== EMAIL VERIFICATION (TEST MODE) =====');
        console.log('To:', email);
        console.log('Subject:', mailOptions.subject);
        console.log('Verification URL:', verificationUrl);
        console.log('===============================================\n');
        return true;
      } else {
        await this.transporter.sendMail(mailOptions);
        console.log('Verification email sent to:', email);
        return true;
      }
    } catch (error) {
      console.error('Error sending verification email:', error);
      return false;
    }
  }

  // Send password reset email
  async sendPasswordResetEmail(email, username, resetToken) {
    const resetUrl = `${process.env.FRONTEND_URL}/reset-password?token=${resetToken}`;
    
    const mailOptions = {
      from: process.env.EMAIL_USER,
      to: email,
      subject: 'Reset Your PanlasApp Password',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #20C5AF;">Password Reset Request</h2>
          <p>Hi ${username},</p>
          <p>You requested to reset your password for your PanlasApp account.</p>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${resetUrl}" 
               style="background-color: #20C5AF; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
              Reset Password
            </a>
          </div>
          <p>Or copy and paste this link in your browser:</p>
          <p style="word-break: break-all; color: #666;">${resetUrl}</p>
          <p style="color: #666; font-size: 12px;">This link will expire in 1 hour.</p>
          <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
          <p style="color: #666; font-size: 12px;">
            If you didn't request a password reset, please ignore this email. Your password will remain unchanged.
          </p>
        </div>
      `
    };

    try {
      if (this.isTestMode) {
        console.log('\n📧 ===== PASSWORD RESET EMAIL (TEST MODE) =====');
        console.log('To:', email);
        console.log('Subject:', mailOptions.subject);
        console.log('Reset URL:', resetUrl);
        console.log('==============================================\n');
        return true;
      } else {
        await this.transporter.sendMail(mailOptions);
        console.log('Password reset email sent to:', email);
        return true;
      }
    } catch (error) {
      console.error('Error sending password reset email:', error);
      return false;
    }
  }

  // Send welcome email after verification
  async sendWelcomeEmail(email, username) {
    const mailOptions = {
      from: process.env.EMAIL_USER,
      to: email,
      subject: 'Welcome to PanlasApp!',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #20C5AF;">Welcome to PanlasApp!</h2>
          <p>Hi ${username},</p>
          <p>Your email has been verified successfully! Welcome to the PanlasApp community.</p>
          <p>You can now:</p>
          <ul>
            <li>Create personalized meal plans</li>
            <li>Discover Filipino recipes</li>
            <li>Track your nutrition</li>
            <li>Manage family meal preferences</li>
          </ul>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${process.env.FRONTEND_URL}/home" 
               style="background-color: #20C5AF; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
              Start Planning Meals
            </a>
          </div>
          <p>Happy meal planning!</p>
          <p>The PanlasApp Team</p>
        </div>
      `
    };

    try {
      if (this.isTestMode) {
        console.log('\n📧 ===== WELCOME EMAIL (TEST MODE) =====');
        console.log('To:', email);
        console.log('Subject:', mailOptions.subject);
        console.log('Welcome message sent to:', username);
        console.log('=======================================\n');
        return true;
      } else {
        await this.transporter.sendMail(mailOptions);
        console.log('Welcome email sent to:', email);
        return true;
      }
    } catch (error) {
      console.error('Error sending welcome email:', error);
      return false;
    }
  }
}

module.exports = new EmailService();
