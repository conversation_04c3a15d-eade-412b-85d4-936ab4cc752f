const nodemailer = require('nodemailer');
const crypto = require('crypto');

class EmailService {
  constructor() {
    // Check if email credentials are configured
    if (process.env.EMAIL_USER && process.env.EMAIL_PASS &&
        process.env.EMAIL_USER !== '<EMAIL>') {
      // Use real email service
      this.transporter = nodemailer.createTransport({
        service: 'gmail',
        auth: {
          user: process.env.EMAIL_USER,
          pass: process.env.EMAIL_PASS
        }
      });
      this.isTestMode = false;
      console.log('📧 Email service initialized with real SMTP');
    } else {
      // Use test mode (console logging)
      console.log('📧 Email service running in TEST MODE - emails will be logged to console');
      this.transporter = null;
      this.isTestMode = true;
    }
  }

  // Generate verification token
  generateVerificationToken() {
    return crypto.randomBytes(32).toString('hex');
  }

  // Generate OTP
  generateOTP() {
    return Math.floor(100000 + Math.random() * 900000).toString(); // 6-digit OTP
  }

  // Generate password reset token
  generatePasswordResetToken() {
    return crypto.randomBytes(32).toString('hex');
  }

  // Send email verification with OTP
  async sendVerificationEmail(email, username, verificationToken, otp) {
    const verificationUrl = `${process.env.FRONTEND_URL || 'http://localhost:3000'}/verify-email?token=${verificationToken}`;

    const mailOptions = {
      from: process.env.EMAIL_USER || '<EMAIL>',
      to: email,
      subject: 'Verify Your PanlasApp Account',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #20C5AF; margin: 0;">PanlasApp</h1>
          </div>

          <h2 style="color: #333;">Welcome to PanlasApp!</h2>
          <p>Hi ${username},</p>
          <p>Thank you for registering with PanlasApp. To complete your registration, please verify your email address.</p>

          <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; text-align: center;">
            <h3 style="color: #333; margin-top: 0;">Your Verification Code:</h3>
            <div style="font-size: 32px; font-weight: bold; color: #20C5AF; letter-spacing: 5px; margin: 10px 0;">
              ${otp}
            </div>
            <p style="color: #666; font-size: 14px; margin-bottom: 0;">Enter this code in the app to verify your account</p>
          </div>

          <p style="color: #666; font-size: 14px;">
            This verification code will expire in 10 minutes for security reasons.
          </p>

          <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
            <p style="color: #666; font-size: 12px; margin: 0;">
              If you didn't create an account with PanlasApp, please ignore this email.
            </p>
          </div>
        </div>
      `
    };

    try {
      if (this.isTestMode) {
        console.log('\n📧 ===== EMAIL VERIFICATION (TEST MODE) =====');
        console.log('To:', email);
        console.log('Subject:', mailOptions.subject);
        console.log('OTP Code:', otp);
        console.log('Verification URL:', verificationUrl);
        console.log('===============================================\n');
        return true;
      } else {
        await this.transporter.sendMail(mailOptions);
        console.log('Verification email sent to:', email, 'with OTP:', otp);
        return true;
      }
    } catch (error) {
      console.error('Error sending verification email:', error);
      return false;
    }
  }

  async sendPasswordResetEmail(email, username, resetToken) {
    console.log(`📧 Password reset email would be sent to: ${email}`);
    return true;
  }

  async sendWelcomeEmail(email, username) {
    console.log(`📧 Welcome email would be sent to: ${email}`);
    return true;
  }
}

module.exports = new EmailService();
