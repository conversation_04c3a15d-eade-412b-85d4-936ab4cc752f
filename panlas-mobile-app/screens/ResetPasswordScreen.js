import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { API_BASE_URL } from '../config/api';

const ResetPasswordScreen = ({ navigation, route }) => {
  const { email } = route.params || {};
  
  const [resetToken, setResetToken] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [step, setStep] = useState(1); // 1: Enter token, 2: Enter new password

  const validatePassword = (password) => {
    const minLength = 8;
    const hasUpperCase = /[A-Z]/.test(password);
    const hasLowerCase = /[a-z]/.test(password);
    const hasNumbers = /\d/.test(password);
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);

    return {
      isValid: password.length >= minLength && hasUpperCase && hasLowerCase && hasNumbers && hasSpecialChar,
      minLength: password.length >= minLength,
      hasUpperCase,
      hasLowerCase,
      hasNumbers,
      hasSpecialChar,
    };
  };

  const handleVerifyToken = async () => {
    if (!resetToken.trim()) {
      Alert.alert('Error', 'Please enter the reset token');
      return;
    }

    setLoading(true);

    try {
      const response = await fetch(`${API_BASE_URL}/api/password-reset/verify-token`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          token: resetToken.trim(),
        }),
      });

      const data = await response.json();

      if (data.success) {
        setStep(2);
        Alert.alert('Success', 'Token verified! Now enter your new password.');
      } else {
        Alert.alert('Error', data.message || 'Invalid or expired token');
      }
    } catch (error) {
      console.error('Token verification error:', error);
      Alert.alert('Error', 'Network error. Please check your connection and try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleResetPassword = async () => {
    if (!newPassword || !confirmPassword) {
      Alert.alert('Error', 'Please fill in all fields');
      return;
    }

    if (newPassword !== confirmPassword) {
      Alert.alert('Error', 'Passwords do not match');
      return;
    }

    const passwordValidation = validatePassword(newPassword);
    if (!passwordValidation.isValid) {
      Alert.alert(
        'Weak Password',
        'Password must be at least 8 characters long and contain uppercase, lowercase, numbers, and special characters.'
      );
      return;
    }

    setLoading(true);

    try {
      const response = await fetch(`${API_BASE_URL}/api/password-reset/reset`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          token: resetToken.trim(),
          newPassword: newPassword,
        }),
      });

      const data = await response.json();

      if (data.success) {
        Alert.alert(
          'Success!',
          'Your password has been reset successfully. You can now login with your new password.',
          [
            {
              text: 'OK',
              onPress: () => {
                navigation.navigate('Login');
              },
            },
          ]
        );
      } else {
        Alert.alert('Error', data.message || 'Failed to reset password');
      }
    } catch (error) {
      console.error('Password reset error:', error);
      Alert.alert('Error', 'Network error. Please check your connection and try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleBackToLogin = () => {
    navigation.navigate('Login');
  };

  const passwordValidation = validatePassword(newPassword);

  return (
    <LinearGradient colors={['#20C5AF', '#17A2B8']} style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardView}
      >
        <ScrollView contentContainerStyle={styles.scrollContainer}>
          {/* Header */}
          <View style={styles.header}>
            <TouchableOpacity style={styles.backButton} onPress={handleBackToLogin}>
              <Ionicons name="arrow-back" size={24} color="#fff" />
            </TouchableOpacity>
            <Text style={styles.headerTitle}>Reset Password</Text>
          </View>

          {/* Main Content */}
          <View style={styles.content}>
            <View style={styles.iconContainer}>
              <Ionicons name="key-outline" size={80} color="#fff" />
            </View>

            {step === 1 ? (
              <>
                <Text style={styles.title}>Enter Reset Token</Text>
                <Text style={styles.subtitle}>
                  Please enter the reset token sent to {email || 'your email'}
                </Text>

                {/* Token Input */}
                <View style={styles.inputContainer}>
                  <Ionicons name="shield-checkmark-outline" size={20} color="#666" style={styles.inputIcon} />
                  <TextInput
                    style={styles.input}
                    placeholder="Enter reset token"
                    placeholderTextColor="#999"
                    value={resetToken}
                    onChangeText={setResetToken}
                    autoCapitalize="none"
                    autoCorrect={false}
                    editable={!loading}
                  />
                </View>

                {/* Verify Token Button */}
                <TouchableOpacity
                  style={[styles.actionButton, loading && styles.actionButtonDisabled]}
                  onPress={handleVerifyToken}
                  disabled={loading}
                >
                  {loading ? (
                    <ActivityIndicator color="#fff" size="small" />
                  ) : (
                    <Text style={styles.actionButtonText}>Verify Token</Text>
                  )}
                </TouchableOpacity>
              </>
            ) : (
              <>
                <Text style={styles.title}>Create New Password</Text>
                <Text style={styles.subtitle}>
                  Enter your new password below
                </Text>

                {/* New Password Input */}
                <View style={styles.inputContainer}>
                  <Ionicons name="lock-closed-outline" size={20} color="#666" style={styles.inputIcon} />
                  <TextInput
                    style={styles.input}
                    placeholder="New password"
                    placeholderTextColor="#999"
                    value={newPassword}
                    onChangeText={setNewPassword}
                    secureTextEntry={!showPassword}
                    autoCapitalize="none"
                    autoCorrect={false}
                    editable={!loading}
                  />
                  <TouchableOpacity onPress={() => setShowPassword(!showPassword)}>
                    <Ionicons
                      name={showPassword ? 'eye-off-outline' : 'eye-outline'}
                      size={20}
                      color="#666"
                    />
                  </TouchableOpacity>
                </View>

                {/* Confirm Password Input */}
                <View style={styles.inputContainer}>
                  <Ionicons name="lock-closed-outline" size={20} color="#666" style={styles.inputIcon} />
                  <TextInput
                    style={styles.input}
                    placeholder="Confirm new password"
                    placeholderTextColor="#999"
                    value={confirmPassword}
                    onChangeText={setConfirmPassword}
                    secureTextEntry={!showConfirmPassword}
                    autoCapitalize="none"
                    autoCorrect={false}
                    editable={!loading}
                  />
                  <TouchableOpacity onPress={() => setShowConfirmPassword(!showConfirmPassword)}>
                    <Ionicons
                      name={showConfirmPassword ? 'eye-off-outline' : 'eye-outline'}
                      size={20}
                      color="#666"
                    />
                  </TouchableOpacity>
                </View>

                {/* Password Strength Indicator */}
                {newPassword.length > 0 && (
                  <View style={styles.passwordStrength}>
                    <Text style={styles.strengthTitle}>Password Requirements:</Text>
                    <View style={styles.strengthItem}>
                      <Ionicons
                        name={passwordValidation.minLength ? 'checkmark-circle' : 'close-circle'}
                        size={16}
                        color={passwordValidation.minLength ? '#4CAF50' : '#FF6B6B'}
                      />
                      <Text style={[styles.strengthText, { color: passwordValidation.minLength ? '#4CAF50' : '#FF6B6B' }]}>
                        At least 8 characters
                      </Text>
                    </View>
                    <View style={styles.strengthItem}>
                      <Ionicons
                        name={passwordValidation.hasUpperCase ? 'checkmark-circle' : 'close-circle'}
                        size={16}
                        color={passwordValidation.hasUpperCase ? '#4CAF50' : '#FF6B6B'}
                      />
                      <Text style={[styles.strengthText, { color: passwordValidation.hasUpperCase ? '#4CAF50' : '#FF6B6B' }]}>
                        Uppercase letter
                      </Text>
                    </View>
                    <View style={styles.strengthItem}>
                      <Ionicons
                        name={passwordValidation.hasLowerCase ? 'checkmark-circle' : 'close-circle'}
                        size={16}
                        color={passwordValidation.hasLowerCase ? '#4CAF50' : '#FF6B6B'}
                      />
                      <Text style={[styles.strengthText, { color: passwordValidation.hasLowerCase ? '#4CAF50' : '#FF6B6B' }]}>
                        Lowercase letter
                      </Text>
                    </View>
                    <View style={styles.strengthItem}>
                      <Ionicons
                        name={passwordValidation.hasNumbers ? 'checkmark-circle' : 'close-circle'}
                        size={16}
                        color={passwordValidation.hasNumbers ? '#4CAF50' : '#FF6B6B'}
                      />
                      <Text style={[styles.strengthText, { color: passwordValidation.hasNumbers ? '#4CAF50' : '#FF6B6B' }]}>
                        Number
                      </Text>
                    </View>
                    <View style={styles.strengthItem}>
                      <Ionicons
                        name={passwordValidation.hasSpecialChar ? 'checkmark-circle' : 'close-circle'}
                        size={16}
                        color={passwordValidation.hasSpecialChar ? '#4CAF50' : '#FF6B6B'}
                      />
                      <Text style={[styles.strengthText, { color: passwordValidation.hasSpecialChar ? '#4CAF50' : '#FF6B6B' }]}>
                        Special character
                      </Text>
                    </View>
                  </View>
                )}

                {/* Reset Password Button */}
                <TouchableOpacity
                  style={[styles.actionButton, loading && styles.actionButtonDisabled]}
                  onPress={handleResetPassword}
                  disabled={loading}
                >
                  {loading ? (
                    <ActivityIndicator color="#fff" size="small" />
                  ) : (
                    <Text style={styles.actionButtonText}>Reset Password</Text>
                  )}
                </TouchableOpacity>
              </>
            )}

            {/* Back to Login */}
            <TouchableOpacity style={styles.backToLoginButton} onPress={handleBackToLogin}>
              <Text style={styles.backToLoginText}>
                Remember your password? <Text style={styles.loginLink}>Sign In</Text>
              </Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  keyboardView: {
    flex: 1,
  },
  scrollContainer: {
    flexGrow: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 50,
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  backButton: {
    padding: 8,
    marginRight: 10,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
  },
  content: {
    flex: 1,
    paddingHorizontal: 30,
    paddingTop: 20,
  },
  iconContainer: {
    alignItems: 'center',
    marginBottom: 30,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#fff',
    textAlign: 'center',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    color: '#E8F8F5',
    textAlign: 'center',
    marginBottom: 40,
    lineHeight: 22,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 12,
    paddingHorizontal: 15,
    marginBottom: 20,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  inputIcon: {
    marginRight: 10,
  },
  input: {
    flex: 1,
    height: 50,
    fontSize: 16,
    color: '#333',
  },
  actionButton: {
    backgroundColor: '#FF6B6B',
    borderRadius: 12,
    paddingVertical: 15,
    paddingHorizontal: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 30,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  actionButtonDisabled: {
    backgroundColor: '#FFB3B3',
  },
  actionButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  passwordStrength: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 8,
    padding: 15,
    marginBottom: 20,
  },
  strengthTitle: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  strengthItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 5,
  },
  strengthText: {
    marginLeft: 8,
    fontSize: 12,
  },
  backToLoginButton: {
    alignItems: 'center',
    marginTop: 20,
  },
  backToLoginText: {
    fontSize: 16,
    color: '#E8F8F5',
  },
  loginLink: {
    fontWeight: 'bold',
    color: '#fff',
    textDecorationLine: 'underline',
  },
});

export default ResetPasswordScreen;
