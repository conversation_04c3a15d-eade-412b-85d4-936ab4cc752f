// API Configuration for PanlasApp Mobile
// This file contains the base URL and configuration for API calls

// Base URL for your backend - UPDATE THIS TO YOUR IP ADDRESS
export const API_BASE_URL = 'http://*************:5000';

// API endpoints
export const API_ENDPOINTS = {
  // Auth endpoints
  LOGIN: '/api/users/login',
  REGISTER: '/api/users/signup',
  VERIFY_OTP: '/api/users/verify-otp',
  RESEND_OTP: '/api/users/resend-otp',
  
  // Password reset endpoints
  FORGOT_PASSWORD: '/api/password-reset/forgot',
  VERIFY_RESET_TOKEN: '/api/password-reset/verify-token',
  RESET_PASSWORD: '/api/password-reset/reset',
  
  // User endpoints
  PROFILE: '/api/users/profile',
  UPDATE_PROFILE: '/api/users/profile',
  FAMILY_MEMBERS: '/api/users/family-members',
  DIETARY_PREFERENCES: '/api/users/dietary-preferences',
  FAVORITE_MEALS: '/api/users/favorite-meals',
  RECENTLY_VIEWED: '/api/users/recently-viewed-meals',
  
  // Meals endpoints
  FILIPINO_MEALS: '/api/meals/filipino',
  POPULAR_MEALS: '/api/meals/popular/list',
  SEARCH_MEALS: '/api/meals/search/filters',
  MEAL_BY_ID: '/api/meals/filipino',
  
  // Meal plans endpoints
  MEAL_PLANS: '/api/meal-plans',
  SAVED_MEAL_PLANS: '/api/meal-plans/saved',
  
  // Activity endpoints
  ACTIVITIES: '/api/activity',
  
  // Feedback endpoints
  FEEDBACK: '/api/feedback'
};

// Image base URL
export const IMAGE_BASE_URL = 'http://*************:5000';

// Helper function to fix image URLs
export const fixImageUrl = (imageUrl) => {
  if (!imageUrl) return null;
  if (imageUrl.startsWith('http')) return imageUrl; // Already a full URL
  if (imageUrl.startsWith('/imagesfood/')) {
    return `${IMAGE_BASE_URL}${imageUrl}`;
  }
  return imageUrl;
};

// API configuration
export const API_CONFIG = {
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
};

// Development/Production environment detection
export const isDevelopment = __DEV__;
export const isProduction = !__DEV__;

// Export default configuration
export default {
  API_BASE_URL,
  API_ENDPOINTS,
  IMAGE_BASE_URL,
  API_CONFIG,
  fixImageUrl,
  isDevelopment,
  isProduction,
};
