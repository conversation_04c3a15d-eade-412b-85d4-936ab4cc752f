import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  FlatList,
  TouchableOpacity,
  Image,
  RefreshControl,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useFavorites } from '../../context/FavoritesContext';
import { colors, fonts, spacing, borderRadius } from '../../styles/theme';
import { commonStyles } from '../../styles/commonStyles';

const FavoritesScreen = ({ navigation }) => {
  const { favorites, loading, loadFavorites, removeFavorite, addFavoriteMealPlan, removeFavoriteMealPlan } = useFavorites();

  const handleMealPress = (meal) => {
    navigation.navigate('Home', {
      screen: 'MealDetail',
      params: { meal }
    });
  };

  const handleRemoveFavorite = async (mealId) => {
    await removeFavorite(mealId);
  };

  const renderFavoriteItem = ({ item: meal }) => (
    <TouchableOpacity
      style={commonStyles.foodCard}
      onPress={() => handleMealPress(meal)}
    >
      <View style={commonStyles.foodCardImage}>
        <Image
          source={{ uri: meal.image || 'https://via.placeholder.com/300x200' }}
          style={styles.mealImage}
          resizeMode="cover"
        />
        <TouchableOpacity
          style={styles.removeButton}
          onPress={() => handleRemoveFavorite(meal.id || meal._id)}
        >
          <Ionicons name="heart" size={24} color={colors.secondary} />
        </TouchableOpacity>
      </View>
      <View style={commonStyles.foodCardContent}>
        <Text style={commonStyles.foodCardTitle} numberOfLines={2}>
          {meal.name}
        </Text>
        <View style={commonStyles.foodCardMeta}>
          <View style={commonStyles.categoryTag}>
            <Text style={commonStyles.categoryTagText}>
              {Array.isArray(meal.category) ? meal.category[0] : meal.category}
            </Text>
          </View>
          <View style={commonStyles.rating}>
            <Ionicons name="star" size={16} color="#FFD700" />
            <Text style={commonStyles.ratingText}>
              {meal.rating || '4.5'}
            </Text>
          </View>
        </View>
        {meal.description && (
          <Text style={styles.mealDescription} numberOfLines={2}>
            {meal.description}
          </Text>
        )}
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={commonStyles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Favorites</Text>
        <Text style={styles.headerSubtitle}>
          {favorites.length} saved item{favorites.length !== 1 ? 's' : ''}
        </Text>
      </View>

      {/* Favorites List */}
      <FlatList
        data={favorites}
        renderItem={renderFavoriteItem}
        keyExtractor={(item) => (item.id || item._id).toString()}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl
            refreshing={loading}
            onRefresh={loadFavorites}
            colors={[colors.primary]}
          />
        }
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons name="heart-outline" size={64} color={colors.textSecondary} />
            <Text style={styles.emptyTitle}>No favorites yet</Text>
            <Text style={styles.emptySubtitle}>
              Start adding meals and meal plans to your favorites to see them here
            </Text>
            <TouchableOpacity
              style={styles.browseButton}
              onPress={() => navigation.navigate('Home')}
            >
              <Text style={styles.browseButtonText}>Browse Meals</Text>
            </TouchableOpacity>
          </View>
        }
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  header: {
    backgroundColor: colors.primary,
    paddingVertical: spacing.lg,
    paddingHorizontal: spacing.md,
  },
  headerTitle: {
    fontSize: fonts.sizes.xlarge,
    fontWeight: 'bold',
    color: colors.surface,
  },
  headerSubtitle: {
    fontSize: fonts.sizes.medium,
    color: colors.surface,
    opacity: 0.8,
    marginTop: spacing.xs,
  },
  listContainer: {
    padding: spacing.md,
  },
  mealImage: {
    width: '100%',
    height: 200,
  },
  removeButton: {
    position: 'absolute',
    top: spacing.sm,
    right: spacing.sm,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: borderRadius.round,
    padding: spacing.sm,
  },
  mealDescription: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
    marginTop: spacing.xs,
    lineHeight: 18,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.xxl,
    paddingHorizontal: spacing.lg,
  },
  emptyTitle: {
    fontSize: fonts.sizes.large,
    fontWeight: 'bold',
    color: colors.textSecondary,
    marginTop: spacing.md,
  },
  emptySubtitle: {
    fontSize: fonts.sizes.medium,
    color: colors.textSecondary,
    marginTop: spacing.sm,
    textAlign: 'center',
    lineHeight: 22,
  },
  browseButton: {
    backgroundColor: colors.primary,
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.lg,
    borderRadius: borderRadius.medium,
    marginTop: spacing.lg,
  },
  browseButtonText: {
    color: colors.surface,
    fontSize: fonts.sizes.medium,
    fontWeight: 'bold',
  },
});

export default FavoritesScreen;
