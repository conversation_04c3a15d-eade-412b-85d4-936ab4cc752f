import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  mockMeals,
  mockFamilyMembers,
  mockRecentlyViewed,
  mockRecentlyAddedToMealPlans,
  mockMealPlans,
  mockSavedMealPlans
} from './mockData';

// Base URL for your backend - UPDATE THIS TO YOUR IP ADDRESS
const BASE_URL = 'http://*************:5000/api';
const IMAGE_BASE_URL = 'http://*************:5000';

// Helper function to fix image URLs
const fixImageUrl = (imageUrl) => {
  if (!imageUrl) return null;
  if (imageUrl.startsWith('http')) return imageUrl; // Already a full URL
  if (imageUrl.startsWith('/imagesfood/')) {
    return `${IMAGE_BASE_URL}${imageUrl}`;
  }
  return imageUrl;
};

// Helper function to process meal data and fix image URLs
const processMealData = (meals) => {
  if (!Array.isArray(meals)) return meals;
  return meals.map(meal => ({
    ...meal,
    image: fixImageUrl(meal.image)
  }));
};

// Create axios instance
const api = axios.create({
  baseURL: BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  async (config) => {
    try {
      const token = await AsyncStorage.getItem('token');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    } catch (error) {
      console.error('Error getting token:', error);
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    // Handle network errors gracefully
    if (error.code === 'NETWORK_ERROR' || error.message === 'Network Error') {
      console.warn('Network error - backend may be unavailable:', error.message);
      // Return a mock response for development
      return Promise.resolve({
        data: [],
        status: 200,
        statusText: 'OK (Mock Response)'
      });
    }

    if (error.response?.status === 401) {
      // Token expired or invalid
      await AsyncStorage.removeItem('token');
      await AsyncStorage.removeItem('user');
    }
    return Promise.reject(error);
  }
);

// Auth API calls
export const authAPI = {
  login: (credentials) => api.post('/users/login', credentials).catch(() => ({ data: {} })),
  register: (userData) => api.post('/users/signup', userData).catch(() => ({ data: {} })),
  getProfile: () => api.get('/users/profile').catch(() => ({ data: {} })),
  verifyEmail: (token) => api.get(`/users/verify-email?token=${token}`).catch(() => ({ data: {} })),
  resendVerification: (email) => api.post('/users/resend-verification', { email }).catch(() => ({ data: {} })),
  requestPasswordReset: (email) => api.post('/users/request-password-reset', { email }).catch(() => ({ data: {} })),
  resetPassword: (token, newPassword) => api.post('/users/reset-password', { token, newPassword }).catch(() => ({ data: {} })),
  // OTP methods (for future implementation)
  verifyOTP: (email, otp, type) => api.post('/users/verify-otp', { email, otp, type }).catch(() => ({ data: {} })),
  resendOTP: (email, type) => api.post('/users/resend-otp', { email, type }).catch(() => ({ data: {} })),
  updateProfile: (data) => api.put('/users/profile', data).catch(() => ({ data: {} })),
  changePassword: (data) => api.put('/users/change-password', data).catch(() => ({ data: {} })),
};

// Meals API calls
export const mealsAPI = {
  getFilipinoDishes: (params) => api.get('/meals/filipino', { params })
    .then(response => {
      const meals = response.data.meals || response.data || [];
      return { data: processMealData(meals) };
    })
    .catch(() => ({ data: mockMeals })),
  getMealById: (id) => api.get(`/meals/filipino/${id}`)
    .then(response => {
      const meal = response.data.meal || response.data || null;
      return { data: meal ? { ...meal, image: fixImageUrl(meal.image) } : null };
    })
    .catch(() => ({
      data: mockMeals.find(meal => meal.id === parseInt(id)) || null
    })),
  searchMeals: (params) => api.get('/meals/search/filters', { params })
    .then(response => {
      const meals = response.data.meals || response.data || [];
      return { data: processMealData(meals) };
    })
    .catch(() => ({ data: mockMeals })),
  getMealSuggestions: (params) => api.get('/meals/suggestions/type', { params })
    .then(response => {
      const meals = response.data.meals || response.data || [];
      return { data: processMealData(meals) };
    })
    .catch(() => ({ data: mockMeals.slice(0, 4) })),
  getPopularMeals: () => api.get('/meals/popular/list')
    .then(response => {
      const meals = response.data.popularMeals || response.data || [];
      return { data: processMealData(meals) };
    })
    .catch(() => ({ data: mockMeals.slice(0, 6) })),
  createMeal: (mealData) => api.post('/meals', mealData).catch(() => ({ data: {} })),
};

// Meal Plans API calls
export const mealPlansAPI = {
  // Get all meal plans for authenticated user with optional date range
  getMealPlans: (params = {}) => {
    const { startDate, endDate } = params;
    const queryParams = {};
    if (startDate) queryParams.startDate = startDate;
    if (endDate) queryParams.endDate = endDate;

    return api.get('/meal-plans', { params: queryParams })
      .then(response => {
        console.log('Meal plans API response:', response.data);
        return { data: response.data || [] };
      })
      .catch(error => {
        console.log('Meal plans API failed, using mock data:', error.message);
        return { data: mockMealPlans || [] };
      });
  },

  // Get specific meal plan by date
  getMealPlanByDate: (date) => {
    return api.get(`/meal-plans/${date}`)
      .then(response => ({ data: response.data }))
      .catch(() => ({
        data: mockMealPlans?.find(plan => plan.date === date) || null
      }));
  },

  // Create or update meal plan (date, mealType, meal)
  createOrUpdateMealPlan: (data) => {
    return api.post('/meal-plans', data)
      .then(response => ({ data: response.data }))
      .catch(error => {
        console.error('Create/update meal plan failed:', error);
        return { data: {} };
      });
  },

  // Save complete meal plan as template
  saveMealPlan: (data) => {
    return api.post('/meal-plans/save', data)
      .then(response => ({ data: response.data }))
      .catch(error => {
        console.error('Save meal plan failed:', error);
        return { data: {} };
      });
  },

  // Get saved meal plan templates
  getSavedMealPlans: () => {
    return api.get('/meal-plans/saved')
      .then(response => ({ data: response.data || [] }))
      .catch(() => ({ data: mockSavedMealPlans || [] }));
  },

  // Favorite meal plan templates
  getFavoriteMealPlans: () => {
    return api.get('/users/saved-meal-plans')
      .then(response => ({ data: response.data || [] }))
      .catch(() => ({ data: [] }));
  },

  addFavoriteMealPlan: (mealPlanData) => {
    return api.post('/users/saved-meal-plans', mealPlanData)
      .then(response => ({ data: response.data }))
      .catch(error => {
        console.error('Add favorite meal plan failed:', error);
        return { data: {} };
      });
  },

  removeFavoriteMealPlan: (templateId) => {
    return api.delete(`/users/saved-meal-plans/${templateId}`)
      .then(response => ({ data: response.data }))
      .catch(error => {
        console.error('Remove favorite meal plan failed:', error);
        return { data: {} };
      });
  },

  // Get user's meal plans with filtering
  getUserMealPlans: (params = {}) => {
    return api.get('/meal-plans/user/plans', { params })
      .then(response => ({ data: response.data?.mealPlans || [] }))
      .catch(() => ({ data: mockMealPlans || [] }));
  },

  // Toggle meal plan lock status
  toggleLockMealPlan: (date, data) => {
    return api.put(`/meal-plans/${date}/lock`, data)
      .then(response => ({ data: response.data }))
      .catch(error => {
        console.error('Toggle lock failed:', error);
        return { data: {} };
      });
  },

  // Mark meal as completed/incomplete
  markMealCompleted: (date, data) => {
    return api.put(`/meal-plans/${date}/complete`, data)
      .then(response => ({ data: response.data }))
      .catch(error => {
        console.error('Mark meal completed failed:', error);
        return { data: {} };
      });
  },

  // Remove meal from plan
  removeMealFromPlan: (date, data) => {
    return api.delete(`/meal-plans/${date}/meals`, { data })
      .then(response => ({ data: response.data }))
      .catch(error => {
        console.error('Remove meal failed:', error);
        return { data: {} };
      });
  },

  // Delete entire meal plan for a date
  deleteMealPlan: (date) => {
    console.log('🔥 API: deleteMealPlan called with date:', date);
    console.log('🔥 API: Making DELETE request to:', `/meal-plans/${date}`);

    return api.delete(`/meal-plans/${date}`)
      .then(response => {
        console.log('🔥 API: Delete successful, response:', response.data);
        return { data: response.data };
      })
      .catch(error => {
        console.error('🔥 API: Delete meal plan failed:', error);
        console.error('🔥 API: Error response:', error.response?.data);
        console.error('🔥 API: Error status:', error.response?.status);
        throw error; // Re-throw the error so the calling code can handle it
      });
  },

  // Update meal times
  updateMealTimes: (date, data) => {
    return api.put(`/meal-plans/${date}/meal-times`, data)
      .then(response => ({ data: response.data }))
      .catch(error => {
        console.error('Update meal times failed:', error);
        return { data: {} };
      });
  },

  // Create meal plan from template
  createFromTemplate: (data) => {
    return api.post('/meal-plans/from-template', data)
      .then(response => ({ data: response.data }))
      .catch(error => {
        console.error('Create from template failed:', error);
        return { data: {} };
      });
  },

  // Create template from meal plan
  createTemplateFromMealPlan: (data) => {
    return api.post('/meal-plans/create-template', data)
      .then(response => ({ data: response.data }))
      .catch(error => {
        console.error('Create template failed:', error);
        return { data: {} };
      });
  },

  // Get meal recommendations
  getMealRecommendations: (params = {}) => {
    return api.get('/meal-plans/recommendations', { params })
      .then(response => ({ data: response.data || [] }))
      .catch(() => ({ data: mockMeals?.slice(0, 5) || [] }));
  },
};

// User Features API calls
export const userAPI = {
  getFavoriteMeals: () => api.get('/users/favorite-meals').catch(() => ({ data: mockMeals.slice(0, 3) })),
  addFavoriteMeal: (mealData) => api.post('/users/favorite-meals', mealData).catch(() => ({ data: {} })),
  removeFavoriteMeal: (mealId) => api.delete(`/users/favorite-meals/${mealId}`).catch(() => ({ data: {} })),
  getRecentlyViewedMeals: () => api.get('/users/recently-viewed-meals').catch(() => ({ data: mockRecentlyViewed })),
  addRecentlyViewedMeal: (mealData) => api.post('/users/recently-viewed-meals', mealData).catch(() => ({ data: {} })),
  getRecentlyAddedToMealPlans: () => api.get('/users/recently-added-to-meal-plans').catch(() => ({ data: mockRecentlyAddedToMealPlans })),
  addRecentlyAddedToMealPlan: (mealData) => api.post('/users/recently-added-to-meal-plans', mealData).catch(() => ({ data: {} })),
  getDietaryPreferences: () => {
    return api.get('/users/dietary-preferences')
      .then(response => {
        console.log('Dietary preferences API response:', response.data);
        return { data: response.data || {} };
      })
      .catch(error => {
        console.log('Dietary preferences API failed, using mock data:', error.message);
        return { data: { restrictions: ['Vegetarian'], allergies: ['Nuts'], calorieTarget: '2000' } };
      });
  },
  updateDietaryPreferences: (data) => api.put('/users/dietary-preferences', data).catch(() => ({ data: {} })),
  getFamilyMembers: () => {
    return api.get('/users/family-members')
      .then(response => {
        console.log('Family members API response:', response.data);
        // Backend returns { familyMembers: [...] }
        return { data: response.data.familyMembers || [] };
      })
      .catch(error => {
        console.log('Family members API failed, using mock data:', error.message);
        return { data: mockFamilyMembers || [] };
      });
  },
  addFamilyMember: (memberData) => {
    return api.post('/users/family-members', memberData)
      .then(response => {
        console.log('Add family member response:', response.data);
        // Backend returns { success: true, familyMembers: [...] }
        return { data: response.data.familyMembers || [] };
      })
      .catch(error => {
        console.error('Add family member failed:', error);
        return { data: {} };
      });
  },
  removeFamilyMember: (memberId) => {
    return api.delete(`/users/family-members/${memberId}`)
      .then(response => {
        console.log('Remove family member response:', response.data);
        // Backend returns { success: true, familyMembers: [...] }
        return { data: response.data.familyMembers || [] };
      })
      .catch(error => {
        console.error('Remove family member failed:', error);
        return { data: {} };
      });
  },
  updateFamilyMember: (memberId, memberData) => api.put(`/users/family-members/${memberId}`, memberData).catch(() => ({ data: {} })),
};

// Feedback API calls
export const feedbackAPI = {
  submitFeedback: (data) => api.post('/feedback/submit', data),
};

// Activity API calls
export const activityAPI = {
  logActivity: (data) => api.post('/activity/log', data),
};

export default api;
